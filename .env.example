# Server Configuration
SERVER_PORT=8080
ENVIRONMENT=development
READ_TIMEOUT=30s
WRITE_TIMEOUT=30s

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=db_b2c
DB_SCHEMA=cashandcarry
DB_SSLMODE=disable

# JWT Configuration
JWT_ACCESS_SECRET=your-super-secret-access-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_ACCESS_EXPIRY_HRS=1
JWT_REFRESH_EXPIRY_HRS=168

# Redis Configuration (for future use)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
