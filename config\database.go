package config

import (
	"fmt"
	"log"

	"b2c-core-service/domain"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDatabase initializes the database connection and runs migrations
func InitDatabase(config *Config) (*gorm.DB, error) {
	var err error

	// Configure GORM logger
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	if config.IsProduction() {
		gormConfig.Logger = logger.Default.LogMode(logger.Error)
	}

	// Connect to database
	DB, err = gorm.Open(postgres.Open(config.Database.DSN), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := DB.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connection established successfully")

	// Create schema if it doesn't exist
	if err := createSchema(DB, config.Database.Schema); err != nil {
		return nil, fmt.Errorf("failed to create schema: %w", err)
	}

	// Run auto-migration
	if err := runMigrations(DB); err != nil {
		return nil, fmt.Errorf("failed to run migrations: %w", err)
	}

	return DB, nil
}

// createSchema creates the database schema if it doesn't exist
func createSchema(db *gorm.DB, schemaName string) error {
	// Enable UUID extension
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		return fmt.Errorf("failed to create uuid-ossp extension: %w", err)
	}

	// Create schema
	if err := db.Exec(fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName)).Error; err != nil {
		return fmt.Errorf("failed to create schema %s: %w", schemaName, err)
	}

	log.Printf("Schema '%s' created or already exists", schemaName)
	return nil
}

// runMigrations runs GORM auto-migration for all models
func runMigrations(db *gorm.DB) error {
	log.Println("Running database migrations...")

	// List of all models to migrate
	models := []interface{}{
		&domain.User{},
		&domain.Profile{},
		&domain.Address{},
		&domain.LoginLog{},
		&domain.ShopCategory{},
		&domain.Shop{},
		&domain.ShopDetail{},
		&domain.ProductCategory{},
		&domain.Product{},
		&domain.ProductDetail{},
		&domain.ProductRating{},
		&domain.CartItem{},
		&domain.Order{},
		&domain.OrderItem{},
		&domain.Offer{},
		&domain.Coupon{},
		&domain.Notification{},
	}

	// Run auto-migration for each model
	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate model %T: %w", model, err)
		}
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// GetDB returns the database instance
func GetDB() *gorm.DB {
	return DB
}

// CloseDB closes the database connection
func CloseDB() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// Transaction helper function
func WithTransaction(fn func(*gorm.DB) error) error {
	tx := DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// Health check function
func HealthCheck() error {
	if DB == nil {
		return fmt.Errorf("database connection is nil")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}
