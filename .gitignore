# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/
dist/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
coverage.html
coverage.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# Environment files
.env
.env.local
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Docker
.dockerignore

# Temporary files
tmp/
temp/

# Build artifacts
build/
target/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Generated documentation
docs/swagger/

# Test coverage
coverage/

# Local development
local/
.local/

# Kubernetes secrets
secrets/
*.key
*.crt
*.pem

# Helm charts dependencies
deploy/helm/charts/
deploy/helm/Chart.lock

# Migration files (if using external migration tool)
migrations/*.sql

# Cache
.cache/

# Node modules (if using any Node.js tools)
node_modules/

# Python (if using any Python tools)
__pycache__/
*.py[cod]
*$py.class

# Air (live reload tool)
.air.toml
tmp/
