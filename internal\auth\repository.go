package auth

import (
	"b2c-core-service/domain"
	"b2c-core-service/internal/common/errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Repository interface defines auth repository methods
type Repository interface {
	CreateUser(user *domain.User) error
	GetUserByEmail(email string) (*domain.User, error)
	GetUserByID(id uuid.UUID) (*domain.User, error)
	GetUserByPhone(phone string) (*domain.User, error)
	UpdateUser(user *domain.User) error
	CreateLoginLog(log *domain.LoginLog) error
}

// repository implements Repository interface
type repository struct {
	db *gorm.DB
}

// NewRepository creates a new auth repository
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

// Create<PERSON>ser creates a new user
func (r *repository) CreateUser(user *domain.User) error {
	if err := r.db.Create(user).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.ErrUserAlreadyExists
		}
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// GetUserByEmail retrieves a user by email
func (r *repository) GetUserByEmail(email string) (*domain.User, error) {
	var user domain.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.ErrUserNotFound
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &user, nil
}

// GetUserByID retrieves a user by ID
func (r *repository) GetUserByID(id uuid.UUID) (*domain.User, error) {
	var user domain.User
	if err := r.db.Where("id = ?", id).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.ErrUserNotFound
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &user, nil
}

// GetUserByPhone retrieves a user by phone
func (r *repository) GetUserByPhone(phone string) (*domain.User, error) {
	var user domain.User
	if err := r.db.Where("phone = ?", phone).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.ErrUserNotFound
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &user, nil
}

// UpdateUser updates a user
func (r *repository) UpdateUser(user *domain.User) error {
	if err := r.db.Save(user).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// CreateLoginLog creates a login log entry
func (r *repository) CreateLoginLog(log *domain.LoginLog) error {
	if err := r.db.Create(log).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// isDuplicateKeyError checks if the error is a duplicate key constraint violation
func isDuplicateKeyError(err error) bool {
	// PostgreSQL duplicate key error code is 23505
	return err != nil && (
		err.Error() == "ERROR: duplicate key value violates unique constraint" ||
		err.Error() == "UNIQUE constraint failed")
}
