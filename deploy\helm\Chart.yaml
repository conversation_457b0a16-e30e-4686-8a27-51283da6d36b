apiVersion: v2
name: b2c-core-service
description: A Helm chart for B2C Core Service - Grocery E-commerce API

# Chart type: application or library
type: application

# Chart version (SemVer)
version: 0.1.0

# Application version
appVersion: "1.0.0"

# Keywords for searching
keywords:
  - b2c
  - ecommerce
  - grocery
  - api
  - golang
  - microservice

# Maintainers
maintainers:
  - name: B2C Team
    email: <EMAIL>

# Dependencies
dependencies:
  - name: postgresql
    version: "12.1.9"
    repository: "https://charts.bitnami.com/bitnami"
    condition: postgresql.enabled
  - name: redis
    version: "17.4.3"
    repository: "https://charts.bitnami.com/bitnami"
    condition: redis.enabled

# Annotations
annotations:
  category: E-commerce
