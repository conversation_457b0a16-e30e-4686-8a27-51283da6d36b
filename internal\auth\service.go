package auth

import (
	"time"

	"b2c-core-service/domain"
	"b2c-core-service/internal/common/auth"
	"b2c-core-service/internal/common/errors"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// Service interface defines auth service methods
type Service interface {
	Register(req *domain.RegisterRequest) (*domain.LoginResponse, error)
	Login(req *domain.LoginRequest, ip, device string) (*domain.LoginResponse, error)
	RefreshToken(req *domain.RefreshTokenRequest) (*domain.LoginResponse, error)
	GetUserProfile(userID uuid.UUID) (*domain.UserDTO, error)
	VerifyUser(userID uuid.UUID) error
}

// service implements Service interface
type service struct {
	repo       Repository
	jwtService *auth.JWTService
}

// NewService creates a new auth service
func NewService(repo Repository, jwtService *auth.JWTService) Service {
	return &service{
		repo:       repo,
		jwtService: jwtService,
	}
}

// Register creates a new user account
func (s *service) Register(req *domain.RegisterRequest) (*domain.LoginResponse, error) {
	// Check if user already exists
	if _, err := s.repo.GetUserByEmail(req.Email); err == nil {
		return nil, errors.ErrUserAlreadyExists
	}

	if _, err := s.repo.GetUserByPhone(req.Phone); err == nil {
		return nil, errors.ErrUserAlreadyExists
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, errors.NewInternalError("Failed to hash password")
	}

	// Create user
	user := &domain.User{
		ID:         uuid.New(),
		Email:      req.Email,
		Phone:      req.Phone,
		Password:   string(hashedPassword),
		IsVerified: false,
		AppCoins:   0,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	if err := s.repo.CreateUser(user); err != nil {
		return nil, err
	}

	// Generate tokens
	tokenPair, err := s.jwtService.GenerateTokenPair(user.ID, user.Email)
	if err != nil {
		return nil, errors.NewInternalError("Failed to generate tokens")
	}

	return &domain.LoginResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
		User: domain.UserDTO{
			ID:         user.ID,
			Email:      user.Email,
			Phone:      user.Phone,
			IsVerified: user.IsVerified,
			AppCoins:   user.AppCoins,
			CreatedAt:  user.CreatedAt,
		},
	}, nil
}

// Login authenticates a user
func (s *service) Login(req *domain.LoginRequest, ip, device string) (*domain.LoginResponse, error) {
	// Get user by email
	user, err := s.repo.GetUserByEmail(req.Email)
	if err != nil {
		return nil, errors.ErrInvalidCredentials
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, errors.ErrInvalidCredentials
	}

	// Create login log
	loginLog := &domain.LoginLog{
		ID:        uuid.New(),
		UserID:    user.ID,
		IP:        ip,
		Device:    device,
		Timestamp: time.Now(),
	}
	
	// Don't fail login if logging fails
	_ = s.repo.CreateLoginLog(loginLog)

	// Generate tokens
	tokenPair, err := s.jwtService.GenerateTokenPair(user.ID, user.Email)
	if err != nil {
		return nil, errors.NewInternalError("Failed to generate tokens")
	}

	return &domain.LoginResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
		User: domain.UserDTO{
			ID:         user.ID,
			Email:      user.Email,
			Phone:      user.Phone,
			IsVerified: user.IsVerified,
			AppCoins:   user.AppCoins,
			CreatedAt:  user.CreatedAt,
		},
	}, nil
}

// RefreshToken generates new tokens using refresh token
func (s *service) RefreshToken(req *domain.RefreshTokenRequest) (*domain.LoginResponse, error) {
	// Validate refresh token and get new token pair
	tokenPair, err := s.jwtService.RefreshTokenPair(req.RefreshToken)
	if err != nil {
		return nil, err
	}

	// Get user details from token claims
	claims, err := s.jwtService.ValidateRefreshToken(req.RefreshToken)
	if err != nil {
		return nil, err
	}

	user, err := s.repo.GetUserByID(claims.UserID)
	if err != nil {
		return nil, err
	}

	return &domain.LoginResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
		User: domain.UserDTO{
			ID:         user.ID,
			Email:      user.Email,
			Phone:      user.Phone,
			IsVerified: user.IsVerified,
			AppCoins:   user.AppCoins,
			CreatedAt:  user.CreatedAt,
		},
	}, nil
}

// GetUserProfile retrieves user profile
func (s *service) GetUserProfile(userID uuid.UUID) (*domain.UserDTO, error) {
	user, err := s.repo.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	return &domain.UserDTO{
		ID:         user.ID,
		Email:      user.Email,
		Phone:      user.Phone,
		IsVerified: user.IsVerified,
		AppCoins:   user.AppCoins,
		CreatedAt:  user.CreatedAt,
	}, nil
}

// VerifyUser marks a user as verified
func (s *service) VerifyUser(userID uuid.UUID) error {
	user, err := s.repo.GetUserByID(userID)
	if err != nil {
		return err
	}

	user.IsVerified = true
	user.UpdatedAt = time.Now()

	return s.repo.UpdateUser(user)
}
