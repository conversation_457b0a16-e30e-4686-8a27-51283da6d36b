package middlewares

import (
	"log"
	"net/http"

	"b2c-core-service/domain"
	"b2c-core-service/internal/common/errors"

	"github.com/gin-gonic/gin"
)

// ErrorMiddleware handles errors and returns appropriate responses
func ErrorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Check if there are any errors
		if len(c.Errors) > 0 {
			err := c.Errors.Last().Err

			// Check if it's an AppError
			if appErr, ok := errors.IsAppError(err); ok {
				c.JSON(appErr.Status, domain.APIResponse{
					Success: false,
					Message: appErr.Message,
					Error:   appErr.Code,
				})
				return
			}

			// Log unexpected errors
			log.Printf("Unexpected error: %v", err)

			// Return generic internal server error
			c.JSON(http.StatusInternalServerError, domain.APIResponse{
				Success: false,
				Message: "Internal server error",
				Error:   errors.ErrCodeInternalServer,
			})
		}
	}
}

// AbortWithError is a helper function to abort with an AppError
func AbortWithError(c *gin.Context, err *errors.AppError) {
	c.Error(err)
	c.Abort()
}

// AbortWithInternalError is a helper function to abort with an internal server error
func AbortWithInternalError(c *gin.Context, err error) {
	log.Printf("Internal error: %v", err)
	c.Error(errors.ErrInternalServer)
	c.Abort()
}
