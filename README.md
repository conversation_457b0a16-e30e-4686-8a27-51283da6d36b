# B2C Core Service

A production-grade, modular, and secure API service backend for a grocery e-commerce application built with Go, Gin, GORM, and PostgreSQL. This service follows a vertical, domain-driven architecture and is designed to be deployed with Docker and Kubernetes.

## 🏗️ Architecture

### Tech Stack
- **Language**: Go 1.21
- **Framework**: Gin (HTTP router)
- **ORM**: GORM
- **Database**: PostgreSQL
- **Authentication**: JWT (access + refresh tokens)
- **API Documentation**: Swagger/OpenAPI
- **Containerization**: Docker + Docker Compose
- **Deployment**: Kubernetes with Helm
- **Gateway**: OpenResty (NGINX with Lua)

### Folder Structure (Vertical Domain-Driven Design)
```
b2c-core-service/
├── cmd/                    # Application entry points
│   └── main.go
├── config/                 # Configuration management
│   ├── config.go
│   └── database.go
├── domain/                 # Domain models and DTOs
│   ├── models.go
│   └── dto.go
├── internal/               # Internal application code
│   ├── common/            # Shared components
│   │   ├── auth/          # JWT authentication
│   │   ├── errors/        # Error handling
│   │   └── middlewares/   # HTTP middlewares
│   ├── auth/              # Authentication module
│   ├── user/              # User management (future)
│   ├── product/           # Product catalog (future)
│   ├── cart/              # Shopping cart (future)
│   └── order/             # Order management (future)
├── deploy/                # Deployment configurations
│   ├── helm/              # Kubernetes Helm charts
│   └── nginx/             # NGINX configuration
├── scripts/               # Utility scripts
├── docs/                  # API documentation
└── tests/                 # Test files
```

## 🚀 Quick Start

### Prerequisites
- Go 1.21+
- Docker & Docker Compose
- Make (optional, for convenience commands)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd b2c-core-service
make setup  # or manually copy .env.example to .env
```

### 2. Start Development Environment
```bash
# Start all services (database, redis, api, gateway)
make docker-up

# Or start just the database and run API locally
make db-up
make run
```

### 3. Access the Application
- **API**: http://localhost:8080
- **API Documentation**: http://localhost:8080/docs/index.html
- **Health Check**: http://localhost:8080/health
- **pgAdmin**: http://localhost:5050 (<EMAIL> / admin123)
- **Gateway**: http://localhost (routes to API)

## 📚 API Documentation

### Authentication Endpoints

#### Register User
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "phone": "+**********",
  "password": "password123"
}
```

#### Login
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Refresh Token
```bash
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "your-refresh-token"
}
```

#### Get Profile (Protected)
```bash
GET /api/v1/auth/profile
Authorization: Bearer your-access-token
```

### Example Usage
```bash
# Register a new user
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","phone":"+**********","password":"password123"}'

# Login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get profile (replace TOKEN with actual token)
curl -X GET http://localhost:8080/api/v1/auth/profile \
  -H "Authorization: Bearer TOKEN"
```

## 🗄️ Database Schema

The application uses PostgreSQL with the following main entities:

### Users & Identity
- **User**: Core user information with email, phone, password
- **Profile**: Extended user profile (name, gender, DOB, language)
- **Address**: User addresses with geolocation support
- **LoginLog**: Audit trail for user logins

### Shop & Catalog
- **ShopCategory**: Categories for shops
- **Shop**: Shop/store information
- **ShopDetail**: Extended shop details (license, owner, etc.)
- **ProductCategory**: Hierarchical product categories
- **Product**: Product catalog with pricing and inventory
- **ProductDetail**: Extended product information
- **ProductRating**: User reviews and ratings

### Orders & Cart
- **CartItem**: Shopping cart items
- **Order**: Order information
- **OrderItem**: Individual items in orders

### Marketing
- **Offer**: Promotional offers
- **Coupon**: Discount coupons
- **Notification**: User notifications

## 🛠️ Development

### Available Make Commands
```bash
make help              # Show all available commands
make setup             # Setup development environment
make run               # Run application locally
make build             # Build binary
make test              # Run tests
make test-coverage     # Run tests with coverage
make lint              # Run linter
make docker-up         # Start all services
make docker-down       # Stop all services
make db-up             # Start only database services
make swag-init         # Generate Swagger docs
make dev               # Start development environment
```

### Environment Variables
Key environment variables (see `.env.example`):
```bash
# Server
SERVER_PORT=8080
ENVIRONMENT=development

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=db_b2c
DB_SCHEMA=cashandcarry

# JWT
JWT_ACCESS_SECRET=your-secret-key
JWT_REFRESH_SECRET=your-refresh-secret
JWT_ACCESS_EXPIRY_HRS=1
JWT_REFRESH_EXPIRY_HRS=168
```

### Adding New Modules
To add a new domain module (e.g., `product`):

1. Create the module directory: `internal/product/`
2. Implement the standard files:
   - `repository.go` - Data access layer
   - `service.go` - Business logic layer
   - `handler.go` - HTTP handlers
   - `routes.go` - Route definitions
3. Add routes to `cmd/main.go`
4. Update domain models if needed

## 🐳 Docker Deployment

### Local Development
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Build
```bash
# Build production image
make prod-docker

# Or manually
docker build -t b2c-core-service:prod .
```

## ☸️ Kubernetes Deployment

### Using Helm
```bash
# Install dependencies
helm dependency update deploy/helm/

# Deploy to Kubernetes
helm install b2c-core-service deploy/helm/ \
  --namespace b2c \
  --create-namespace \
  --values deploy/helm/values.yaml

# Upgrade deployment
helm upgrade b2c-core-service deploy/helm/ \
  --namespace b2c

# Uninstall
helm uninstall b2c-core-service --namespace b2c
```

### Manual Kubernetes Deployment
```bash
# Apply configurations
kubectl apply -f deploy/k8s/

# Check status
kubectl get pods -n b2c
kubectl get services -n b2c
```

## 🔒 Security Features

- **JWT Authentication**: Secure access and refresh token implementation
- **Password Hashing**: bcrypt for secure password storage
- **CORS Protection**: Configurable CORS middleware
- **Rate Limiting**: API rate limiting via NGINX
- **Input Validation**: Request validation using Gin binding
- **SQL Injection Protection**: GORM ORM prevents SQL injection
- **Security Headers**: Standard security headers via NGINX

## 📊 Monitoring & Observability

### Health Checks
- Application health endpoint: `/health`
- Database connectivity check
- Kubernetes liveness and readiness probes

### Logging
- Structured logging with request IDs
- Access logs via NGINX
- Application logs with different levels

### Future Enhancements
- Prometheus metrics endpoint
- Distributed tracing with Jaeger
- ELK stack integration

## 🧪 Testing

```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage

# Run specific test
go test ./internal/auth/...
```

## 🚀 Production Considerations

### Performance
- Connection pooling for database
- Redis for caching (configured but not implemented)
- NGINX for load balancing and static content
- Horizontal pod autoscaling in Kubernetes

### Security
- Use strong JWT secrets in production
- Enable SSL/TLS
- Configure proper CORS origins
- Use Kubernetes secrets for sensitive data
- Regular security updates

### Scalability
- Stateless application design
- Database read replicas
- Microservice-ready architecture
- Kubernetes horizontal scaling

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the API documentation at `/docs`
