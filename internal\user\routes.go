package user

import (
	"b2c-core-service/internal/common/auth"
	"b2c-core-service/internal/common/middlewares"

	"github.com/gin-gonic/gin"
)

// SetupRoutes sets up user routes
func SetupRoutes(router *gin.RouterGroup, handler *Handler, jwtService *auth.JWTService) {
	userGroup := router.Group("/user")
	userGroup.Use(middlewares.AuthMiddleware(jwtService))
	{
		// Profile routes
		userGroup.POST("/profile", handler.CreateProfile)
		userGroup.GET("/profile", handler.GetProfile)
		userGroup.PUT("/profile", handler.UpdateProfile)
		userGroup.DELETE("/profile", handler.DeleteProfile)

		// Address routes
		userGroup.POST("/addresses", handler.CreateAddress)
		userGroup.GET("/addresses", handler.GetAddresses)
		userGroup.GET("/addresses/:id", handler.GetAddress)
		userGroup.PUT("/addresses/:id", handler.UpdateAddress)
		userGroup.DELETE("/addresses/:id", handler.DeleteAddress)
		userGroup.PATCH("/addresses/:id/default", handler.SetDefaultAddress)
	}
}
