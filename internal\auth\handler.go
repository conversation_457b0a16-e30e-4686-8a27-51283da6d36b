package auth

import (
	"net/http"

	"b2c-core-service/domain"
	"b2c-core-service/internal/common/errors"
	"b2c-core-service/internal/common/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// <PERSON><PERSON> handles auth HTTP requests
type Handler struct {
	service Service
}

// NewHandler creates a new auth handler
func NewHandler(service Service) *Handler {
	return &Handler{service: service}
}

// Register handles user registration
// @Summary Register a new user
// @Description Create a new user account
// @Tags auth
// @Accept json
// @Produce json
// @Param request body domain.RegisterRequest true "Registration request"
// @Success 201 {object} domain.APIResponse{data=domain.LoginResponse}
// @Failure 400 {object} domain.APIResponse
// @Failure 409 {object} domain.APIResponse
// @Router /auth/register [post]
func (h *Handler) Register(c *gin.Context) {
	var req domain.RegisterRequest
	if err := c.ShouldBind<PERSON>SO<PERSON>(&req); err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError(err.Error()))
		return
	}

	response, err := h.service.Register(&req)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusCreated, domain.APIResponse{
		Success: true,
		Message: "User registered successfully",
		Data:    response,
	})
}

// Login handles user login
// @Summary Login user
// @Description Authenticate user and return tokens
// @Tags auth
// @Accept json
// @Produce json
// @Param request body domain.LoginRequest true "Login request"
// @Success 200 {object} domain.APIResponse{data=domain.LoginResponse}
// @Failure 400 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Router /auth/login [post]
func (h *Handler) Login(c *gin.Context) {
	var req domain.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError(err.Error()))
		return
	}

	// Get client info
	ip := c.ClientIP()
	device := c.GetHeader("User-Agent")

	response, err := h.service.Login(&req, ip, device)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Login successful",
		Data:    response,
	})
}

// RefreshToken handles token refresh
// @Summary Refresh access token
// @Description Generate new access token using refresh token
// @Tags auth
// @Accept json
// @Produce json
// @Param request body domain.RefreshTokenRequest true "Refresh token request"
// @Success 200 {object} domain.APIResponse{data=domain.LoginResponse}
// @Failure 400 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Router /auth/refresh [post]
func (h *Handler) RefreshToken(c *gin.Context) {
	var req domain.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError(err.Error()))
		return
	}

	response, err := h.service.RefreshToken(&req)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Token refreshed successfully",
		Data:    response,
	})
}

// GetProfile handles getting user profile
// @Summary Get user profile
// @Description Get current user's profile information
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} domain.APIResponse{data=domain.UserDTO}
// @Failure 401 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /auth/profile [get]
func (h *Handler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	profile, err := h.service.GetUserProfile(userID.(uuid.UUID))
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Profile retrieved successfully",
		Data:    profile,
	})
}

// VerifyUser handles user verification
// @Summary Verify user account
// @Description Mark user account as verified
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /auth/verify [post]
func (h *Handler) VerifyUser(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	err := h.service.VerifyUser(userID.(uuid.UUID))
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "User verified successfully",
	})
}
