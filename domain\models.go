package domain

import (
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Users & Identity
type User struct {
	ID         uuid.UUID      `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	Email      string         `gorm:"uniqueIndex;schema:cashandcarry" json:"email"`
	Phone      string         `gorm:"uniqueIndex;schema:cashandcarry" json:"phone"`
	Password   string         `gorm:"schema:cashandcarry" json:"-"`
	IsVerified bool           `gorm:"default:false;schema:cashandcarry" json:"is_verified"`
	AppCoins   int            `gorm:"default:0;schema:cashandcarry" json:"app_coins"`
	CreatedAt  time.Time      `gorm:"schema:cashandcarry" json:"created_at"`
	UpdatedAt  time.Time      `gorm:"schema:cashandcarry" json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index;schema:cashandcarry" json:"-"`
}

type Profile struct {
	ID        uuid.UUID  `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	UserID    uuid.UUID  `gorm:"index;schema:cashandcarry" json:"user_id"`
	FirstName string     `gorm:"schema:cashandcarry" json:"first_name"`
	LastName  string     `gorm:"schema:cashandcarry" json:"last_name"`
	Gender    string     `gorm:"schema:cashandcarry" json:"gender"`
	DOB       *time.Time `gorm:"schema:cashandcarry" json:"dob"`
	Language  string     `gorm:"default:'en';schema:cashandcarry" json:"language"`
	CreatedAt time.Time  `gorm:"schema:cashandcarry" json:"created_at"`
	UpdatedAt time.Time  `gorm:"schema:cashandcarry" json:"updated_at"`
}

type Address struct {
	ID        uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	UserID    uuid.UUID `gorm:"index;schema:cashandcarry" json:"user_id"`
	Line1     string    `gorm:"schema:cashandcarry" json:"line1"`
	Line2     string    `gorm:"schema:cashandcarry" json:"line2"`
	City      string    `gorm:"schema:cashandcarry" json:"city"`
	State     string    `gorm:"schema:cashandcarry" json:"state"`
	Pincode   string    `gorm:"index;schema:cashandcarry" json:"pincode"`
	Location  string    `gorm:"schema:cashandcarry" json:"location"` // GPS coordinates
	Type      string    `gorm:"schema:cashandcarry" json:"type"`     // Home, Work, Other
	IsDefault bool      `gorm:"default:false;schema:cashandcarry" json:"is_default"`
	CreatedAt time.Time `gorm:"schema:cashandcarry" json:"created_at"`
	UpdatedAt time.Time `gorm:"schema:cashandcarry" json:"updated_at"`
}

type LoginLog struct {
	ID        uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	UserID    uuid.UUID `gorm:"index;schema:cashandcarry" json:"user_id"`
	IP        string    `gorm:"schema:cashandcarry" json:"ip"`
	Device    string    `gorm:"schema:cashandcarry" json:"device"`
	Timestamp time.Time `gorm:"schema:cashandcarry" json:"timestamp"`
}

// Shop & Category
type ShopCategory struct {
	ID        uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	Name      string    `gorm:"schema:cashandcarry" json:"name"`
	IconURL   string    `gorm:"schema:cashandcarry" json:"icon_url"`
	CreatedAt time.Time `gorm:"schema:cashandcarry" json:"created_at"`
}

type Shop struct {
	ID          uuid.UUID      `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	CategoryID  uuid.UUID      `gorm:"index;schema:cashandcarry" json:"category_id"`
	Name        string         `gorm:"schema:cashandcarry" json:"name"`
	Description string         `gorm:"schema:cashandcarry" json:"description"`
	ImageURL    string         `gorm:"schema:cashandcarry" json:"image_url"`
	Pincode     string         `gorm:"index;schema:cashandcarry" json:"pincode"`
	Rating      float32        `gorm:"default:0;schema:cashandcarry" json:"rating"`
	IsActive    bool           `gorm:"default:true;schema:cashandcarry" json:"is_active"`
	CreatedAt   time.Time      `gorm:"schema:cashandcarry" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"schema:cashandcarry" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index;schema:cashandcarry" json:"-"`
}

type ShopDetail struct {
	ID        uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	ShopID    uuid.UUID `gorm:"index;schema:cashandcarry" json:"shop_id"`
	LicenseNo string    `gorm:"schema:cashandcarry" json:"license_no"`
	OwnerName string    `gorm:"schema:cashandcarry" json:"owner_name"`
	Address   string    `gorm:"schema:cashandcarry" json:"address"`
	GSTIN     string    `gorm:"schema:cashandcarry" json:"gstin"`
	CreatedAt time.Time `gorm:"schema:cashandcarry" json:"created_at"`
}

// Product & Catalog
type ProductCategory struct {
	ID        uuid.UUID  `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	Name      string     `gorm:"schema:cashandcarry" json:"name"`
	ParentID  *uuid.UUID `gorm:"index;schema:cashandcarry" json:"parent_id"` // nested categories
	IconURL   string     `gorm:"schema:cashandcarry" json:"icon_url"`
	CreatedAt time.Time  `gorm:"schema:cashandcarry" json:"created_at"`
}

type Product struct {
	ID           uuid.UUID      `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	ShopID       uuid.UUID      `gorm:"index;schema:cashandcarry" json:"shop_id"`
	CategoryID   uuid.UUID      `gorm:"index;schema:cashandcarry" json:"category_id"`
	Name         string         `gorm:"schema:cashandcarry" json:"name"`
	Slug         string         `gorm:"uniqueIndex;schema:cashandcarry" json:"slug"`
	ThumbnailURL string         `gorm:"schema:cashandcarry" json:"thumbnail_url"`
	Price        float64        `gorm:"schema:cashandcarry" json:"price"`
	StockQty     int            `gorm:"default:0;schema:cashandcarry" json:"stock_qty"`
	Unit         string         `gorm:"schema:cashandcarry" json:"unit"`
	IsAvailable  bool           `gorm:"default:true;schema:cashandcarry" json:"is_available"`
	CreatedAt    time.Time      `gorm:"schema:cashandcarry" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"schema:cashandcarry" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index;schema:cashandcarry" json:"-"`
}

type ProductDetail struct {
	ID          uuid.UUID      `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	ProductID   uuid.UUID      `gorm:"index;schema:cashandcarry" json:"product_id"`
	Description string         `gorm:"schema:cashandcarry" json:"description"`
	ImageURLs   pq.StringArray `gorm:"type:text[];schema:cashandcarry" json:"image_urls"`
	Tags        pq.StringArray `gorm:"type:text[];schema:cashandcarry" json:"tags"`
	SpecsJSON   datatypes.JSON `gorm:"schema:cashandcarry" json:"specs_json"`
}

type ProductRating struct {
	ID        uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	ProductID uuid.UUID `gorm:"index;schema:cashandcarry" json:"product_id"`
	UserID    uuid.UUID `gorm:"index;schema:cashandcarry" json:"user_id"`
	Stars     int       `gorm:"schema:cashandcarry" json:"stars"`
	Review    string    `gorm:"schema:cashandcarry" json:"review"`
	CreatedAt time.Time `gorm:"schema:cashandcarry" json:"created_at"`
}

// Cart & Orders
type CartItem struct {
	ID        uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	UserID    uuid.UUID `gorm:"index;schema:cashandcarry" json:"user_id"`
	ProductID uuid.UUID `gorm:"index;schema:cashandcarry" json:"product_id"`
	Quantity  int       `gorm:"schema:cashandcarry" json:"quantity"`
	AddedAt   time.Time `gorm:"schema:cashandcarry" json:"added_at"`
}

type Order struct {
	ID            uuid.UUID      `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	UserID        uuid.UUID      `gorm:"index;schema:cashandcarry" json:"user_id"`
	ShopID        uuid.UUID      `gorm:"index;schema:cashandcarry" json:"shop_id"`
	AddressID     uuid.UUID      `gorm:"schema:cashandcarry" json:"address_id"`
	TotalAmount   float64        `gorm:"schema:cashandcarry" json:"total_amount"`
	Status        string         `gorm:"default:'Placed';schema:cashandcarry" json:"status"` // Placed, Dispatched, Delivered, Cancelled
	PaymentMethod string         `gorm:"schema:cashandcarry" json:"payment_method"`
	PlacedAt      time.Time      `gorm:"schema:cashandcarry" json:"placed_at"`
	UpdatedAt     time.Time      `gorm:"schema:cashandcarry" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index;schema:cashandcarry" json:"-"`
}

type OrderItem struct {
	ID        uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	OrderID   uuid.UUID `gorm:"index;schema:cashandcarry" json:"order_id"`
	ProductID uuid.UUID `gorm:"index;schema:cashandcarry" json:"product_id"`
	Quantity  int       `gorm:"schema:cashandcarry" json:"quantity"`
	UnitPrice float64   `gorm:"schema:cashandcarry" json:"unit_price"`
	Total     float64   `gorm:"schema:cashandcarry" json:"total"`
}

// Offers & Coupons
type Offer struct {
	ID           uuid.UUID      `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	Title        string         `gorm:"schema:cashandcarry" json:"title"`
	Description  string         `gorm:"schema:cashandcarry" json:"description"`
	BannerURL    string         `gorm:"schema:cashandcarry" json:"banner_url"`
	StartDate    time.Time      `gorm:"schema:cashandcarry" json:"start_date"`
	EndDate      time.Time      `gorm:"schema:cashandcarry" json:"end_date"`
	ApplicableTo string         `gorm:"schema:cashandcarry" json:"applicable_to"` // Category, Shop, Product
	IsActive     bool           `gorm:"default:true;schema:cashandcarry" json:"is_active"`
	CreatedAt    time.Time      `gorm:"schema:cashandcarry" json:"created_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index;schema:cashandcarry" json:"-"`
}

type Coupon struct {
	ID          uuid.UUID      `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	Code        string         `gorm:"uniqueIndex;schema:cashandcarry" json:"code"`
	DiscountPct float64        `gorm:"schema:cashandcarry" json:"discount_pct"`
	MinAmount   float64        `gorm:"schema:cashandcarry" json:"min_amount"`
	MaxDiscount float64        `gorm:"schema:cashandcarry" json:"max_discount"`
	Expiry      time.Time      `gorm:"schema:cashandcarry" json:"expiry"`
	IsActive    bool           `gorm:"default:true;schema:cashandcarry" json:"is_active"`
	CreatedAt   time.Time      `gorm:"schema:cashandcarry" json:"created_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index;schema:cashandcarry" json:"-"`
}

// Notifications
type Notification struct {
	ID        uuid.UUID  `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;schema:cashandcarry" json:"id"`
	UserID    *uuid.UUID `gorm:"index;schema:cashandcarry" json:"user_id"` // nullable for broadcast notifications
	Title     string     `gorm:"schema:cashandcarry" json:"title"`
	Message   string     `gorm:"schema:cashandcarry" json:"message"`
	Type      string     `gorm:"schema:cashandcarry" json:"type"` // Info, Promo, Order
	LinkURL   string     `gorm:"schema:cashandcarry" json:"link_url"`
	IsRead    bool       `gorm:"default:false;schema:cashandcarry" json:"is_read"`
	CreatedAt time.Time  `gorm:"schema:cashandcarry" json:"created_at"`
}
