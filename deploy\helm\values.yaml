# Default values for b2c-core-service

# Application configuration
app:
  name: b2c-core-service
  version: "1.0.0"

# Image configuration
image:
  repository: b2c-core-service
  tag: "latest"
  pullPolicy: IfNotPresent

# Service configuration
service:
  type: ClusterIP
  port: 8080
  targetPort: 8080

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
  hosts:
    - host: api.b2c.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Resource limits and requests
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# Horizontal Pod Autoscaler
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Pod configuration
replicaCount: 2

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1001
  fsGroup: 1001

# Pod security context
podSecurityContext:
  fsGroup: 1001

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Environment variables
env:
  - name: ENVIRONMENT
    value: "production"
  - name: SERVER_PORT
    value: "8080"
  - name: JWT_ACCESS_EXPIRY_HRS
    value: "1"
  - name: JWT_REFRESH_EXPIRY_HRS
    value: "168"

# Secrets (to be created separately)
secrets:
  - name: DB_PASSWORD
    secretName: b2c-db-secret
    secretKey: password
  - name: JWT_ACCESS_SECRET
    secretName: b2c-jwt-secret
    secretKey: access-secret
  - name: JWT_REFRESH_SECRET
    secretName: b2c-jwt-secret
    secretKey: refresh-secret

# ConfigMap configuration
configMap:
  data:
    DB_HOST: "b2c-postgresql"
    DB_PORT: "5432"
    DB_USER: "postgres"
    DB_NAME: "db_b2c"
    DB_SCHEMA: "cashandcarry"
    DB_SSLMODE: "require"
    REDIS_HOST: "b2c-redis-master"
    REDIS_PORT: "6379"

# Health checks
healthCheck:
  enabled: true
  path: /health
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Liveness probe
livenessProbe:
  httpGet:
    path: /health
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Readiness probe
readinessProbe:
  httpGet:
    path: /health
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3

# PostgreSQL configuration (Bitnami chart)
postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres"
    database: "db_b2c"
  primary:
    persistence:
      enabled: true
      size: 10Gi
    resources:
      limits:
        memory: 512Mi
        cpu: 500m
      requests:
        memory: 256Mi
        cpu: 250m

# Redis configuration (Bitnami chart)
redis:
  enabled: true
  auth:
    enabled: false
  master:
    persistence:
      enabled: true
      size: 5Gi
    resources:
      limits:
        memory: 256Mi
        cpu: 250m
      requests:
        memory: 128Mi
        cpu: 100m

# Service Monitor for Prometheus (if using Prometheus Operator)
serviceMonitor:
  enabled: false
  interval: 30s
  path: /metrics
  labels: {}

# Pod Disruption Budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Network Policy
networkPolicy:
  enabled: false
  ingress: []
  egress: []
