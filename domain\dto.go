package domain

import (
	"time"

	"github.com/google/uuid"
)

// Auth DTOs
type RegisterRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Phone    string `json:"phone" binding:"required"`
	Password string `json:"password" binding:"required,min=6"`
}

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	User         UserDTO   `json:"user"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// User DTOs
type UserDTO struct {
	ID         uuid.UUID `json:"id"`
	Email      string    `json:"email"`
	Phone      string    `json:"phone"`
	IsVerified bool      `json:"is_verified"`
	AppCoins   int       `json:"app_coins"`
	CreatedAt  time.Time `json:"created_at"`
}

type ProfileDTO struct {
	ID        uuid.UUID  `json:"id"`
	UserID    uuid.UUID  `json:"user_id"`
	FirstName string     `json:"first_name"`
	LastName  string     `json:"last_name"`
	Gender    string     `json:"gender"`
	DOB       *time.Time `json:"dob"`
	Language  string     `json:"language"`
}

type CreateProfileRequest struct {
	FirstName string     `json:"first_name" binding:"required"`
	LastName  string     `json:"last_name" binding:"required"`
	Gender    string     `json:"gender"`
	DOB       *time.Time `json:"dob"`
	Language  string     `json:"language"`
}

type AddressDTO struct {
	ID        uuid.UUID `json:"id"`
	UserID    uuid.UUID `json:"user_id"`
	Line1     string    `json:"line1"`
	Line2     string    `json:"line2"`
	City      string    `json:"city"`
	State     string    `json:"state"`
	Pincode   string    `json:"pincode"`
	Location  string    `json:"location"`
	Type      string    `json:"type"`
	IsDefault bool      `json:"is_default"`
}

type CreateAddressRequest struct {
	Line1     string `json:"line1" binding:"required"`
	Line2     string `json:"line2"`
	City      string `json:"city" binding:"required"`
	State     string `json:"state" binding:"required"`
	Pincode   string `json:"pincode" binding:"required"`
	Location  string `json:"location"`
	Type      string `json:"type" binding:"required"`
	IsDefault bool   `json:"is_default"`
}

// Product DTOs
type ProductDTO struct {
	ID           uuid.UUID `json:"id"`
	ShopID       uuid.UUID `json:"shop_id"`
	CategoryID   uuid.UUID `json:"category_id"`
	Name         string    `json:"name"`
	Slug         string    `json:"slug"`
	ThumbnailURL string    `json:"thumbnail_url"`
	Price        float64   `json:"price"`
	StockQty     int       `json:"stock_qty"`
	Unit         string    `json:"unit"`
	IsAvailable  bool      `json:"is_available"`
}

type CreateProductRequest struct {
	ShopID       uuid.UUID `json:"shop_id" binding:"required"`
	CategoryID   uuid.UUID `json:"category_id" binding:"required"`
	Name         string    `json:"name" binding:"required"`
	ThumbnailURL string    `json:"thumbnail_url"`
	Price        float64   `json:"price" binding:"required,min=0"`
	StockQty     int       `json:"stock_qty" binding:"min=0"`
	Unit         string    `json:"unit" binding:"required"`
	Description  string    `json:"description"`
	ImageURLs    []string  `json:"image_urls"`
	Tags         []string  `json:"tags"`
}

type ProductSearchRequest struct {
	Query      string    `json:"query"`
	CategoryID uuid.UUID `json:"category_id"`
	ShopID     uuid.UUID `json:"shop_id"`
	MinPrice   float64   `json:"min_price"`
	MaxPrice   float64   `json:"max_price"`
	Pincode    string    `json:"pincode"`
	Page       int       `json:"page"`
	Limit      int       `json:"limit"`
}

// Cart DTOs
type CartItemDTO struct {
	ID        uuid.UUID  `json:"id"`
	UserID    uuid.UUID  `json:"user_id"`
	ProductID uuid.UUID  `json:"product_id"`
	Product   ProductDTO `json:"product,omitempty"`
	Quantity  int        `json:"quantity"`
	AddedAt   time.Time  `json:"added_at"`
}

type AddToCartRequest struct {
	ProductID uuid.UUID `json:"product_id" binding:"required"`
	Quantity  int       `json:"quantity" binding:"required,min=1"`
}

type UpdateCartItemRequest struct {
	Quantity int `json:"quantity" binding:"required,min=0"`
}

// Order DTOs
type OrderDTO struct {
	ID            uuid.UUID     `json:"id"`
	UserID        uuid.UUID     `json:"user_id"`
	ShopID        uuid.UUID     `json:"shop_id"`
	AddressID     uuid.UUID     `json:"address_id"`
	TotalAmount   float64       `json:"total_amount"`
	Status        string        `json:"status"`
	PaymentMethod string        `json:"payment_method"`
	PlacedAt      time.Time     `json:"placed_at"`
	Items         []OrderItemDTO `json:"items,omitempty"`
}

type OrderItemDTO struct {
	ID        uuid.UUID  `json:"id"`
	OrderID   uuid.UUID  `json:"order_id"`
	ProductID uuid.UUID  `json:"product_id"`
	Product   ProductDTO `json:"product,omitempty"`
	Quantity  int        `json:"quantity"`
	UnitPrice float64    `json:"unit_price"`
	Total     float64    `json:"total"`
}

type CreateOrderRequest struct {
	ShopID        uuid.UUID `json:"shop_id" binding:"required"`
	AddressID     uuid.UUID `json:"address_id" binding:"required"`
	PaymentMethod string    `json:"payment_method" binding:"required"`
	Items         []CreateOrderItemRequest `json:"items" binding:"required,min=1"`
}

type CreateOrderItemRequest struct {
	ProductID uuid.UUID `json:"product_id" binding:"required"`
	Quantity  int       `json:"quantity" binding:"required,min=1"`
}

// Shop DTOs
type ShopDTO struct {
	ID          uuid.UUID `json:"id"`
	CategoryID  uuid.UUID `json:"category_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	ImageURL    string    `json:"image_url"`
	Pincode     string    `json:"pincode"`
	Rating      float32   `json:"rating"`
	IsActive    bool      `json:"is_active"`
}

type ShopCategoryDTO struct {
	ID      uuid.UUID `json:"id"`
	Name    string    `json:"name"`
	IconURL string    `json:"icon_url"`
}

// Common Response DTOs
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	Total      int64       `json:"total"`
	TotalPages int         `json:"total_pages"`
}

type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// Notification DTOs
type NotificationDTO struct {
	ID        uuid.UUID  `json:"id"`
	UserID    *uuid.UUID `json:"user_id"`
	Title     string     `json:"title"`
	Message   string     `json:"message"`
	Type      string     `json:"type"`
	LinkURL   string     `json:"link_url"`
	IsRead    bool       `json:"is_read"`
	CreatedAt time.Time  `json:"created_at"`
}
