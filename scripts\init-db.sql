-- Initialize database for B2C Core Service

-- Create the cashandcarry schema
CREATE SCHEMA IF NOT EXISTS cashandcarry;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant permissions
GRANT ALL PRIVILEGES ON SCHEMA cashandcarry TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cashandcarry TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA cashandcarry TO postgres;

-- Set default search path
ALTER DATABASE db_b2c SET search_path TO cashandcarry, public;
