package auth

import (
	"b2c-core-service/internal/common/auth"
	"b2c-core-service/internal/common/middlewares"

	"github.com/gin-gonic/gin"
)

// SetupRoutes sets up auth routes
func SetupRoutes(router *gin.RouterGroup, handler *Handler, jwtService *auth.JWTService) {
	authGroup := router.Group("/auth")
	{
		// Public routes
		authGroup.POST("/register", handler.Register)
		authGroup.POST("/login", handler.Login)
		authGroup.POST("/refresh", handler.RefreshToken)

		// Protected routes
		protected := authGroup.Group("")
		protected.Use(middlewares.AuthMiddleware(jwtService))
		{
			protected.GET("/profile", handler.GetProfile)
			protected.POST("/verify", handler.VerifyUser)
		}
	}
}
