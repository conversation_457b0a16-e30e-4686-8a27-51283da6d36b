package auth

import (
	"testing"
	"time"

	"b2c-core-service/config"
	"b2c-core-service/domain"
	"b2c-core-service/internal/common/auth"
	"b2c-core-service/internal/common/errors"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockRepository is a mock implementation of Repository interface
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) CreateUser(user *domain.User) error {
	args := m.Called(user)
	return args.Error(0)
}

func (m *MockRepository) GetUserByEmail(email string) (*domain.User, error) {
	args := m.Called(email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.User), args.Error(1)
}

func (m *MockRepository) GetUserByID(id uuid.UUID) (*domain.User, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.User), args.Error(1)
}

func (m *MockRepository) GetUserByPhone(phone string) (*domain.User, error) {
	args := m.Called(phone)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.User), args.Error(1)
}

func (m *MockRepository) UpdateUser(user *domain.User) error {
	args := m.Called(user)
	return args.Error(0)
}

func (m *MockRepository) CreateLoginLog(log *domain.LoginLog) error {
	args := m.Called(log)
	return args.Error(0)
}

func TestAuthService_Register(t *testing.T) {
	// Setup
	mockRepo := new(MockRepository)
	cfg := &config.Config{
		JWT: config.JWTConfig{
			AccessSecret:     "test-access-secret",
			RefreshSecret:    "test-refresh-secret",
			AccessExpiryHrs:  1,
			RefreshExpiryHrs: 168,
		},
	}
	jwtService := auth.NewJWTService(cfg)
	service := NewService(mockRepo, jwtService)

	t.Run("successful registration", func(t *testing.T) {
		// Arrange
		req := &domain.RegisterRequest{
			Email:    "<EMAIL>",
			Phone:    "+1234567890",
			Password: "password123",
		}

		// Mock expectations
		mockRepo.On("GetUserByEmail", req.Email).Return(nil, errors.ErrUserNotFound)
		mockRepo.On("GetUserByPhone", req.Phone).Return(nil, errors.ErrUserNotFound)
		mockRepo.On("CreateUser", mock.AnythingOfType("*domain.User")).Return(nil)

		// Act
		response, err := service.Register(req)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.NotEmpty(t, response.AccessToken)
		assert.NotEmpty(t, response.RefreshToken)
		assert.Equal(t, req.Email, response.User.Email)
		assert.Equal(t, req.Phone, response.User.Phone)
		assert.False(t, response.User.IsVerified)

		// Verify mock expectations
		mockRepo.AssertExpectations(t)
	})

	t.Run("user already exists by email", func(t *testing.T) {
		// Arrange
		req := &domain.RegisterRequest{
			Email:    "<EMAIL>",
			Phone:    "+1234567890",
			Password: "password123",
		}

		existingUser := &domain.User{
			ID:    uuid.New(),
			Email: req.Email,
		}

		// Mock expectations
		mockRepo.On("GetUserByEmail", req.Email).Return(existingUser, nil)

		// Act
		response, err := service.Register(req)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, errors.ErrUserAlreadyExists, err)

		// Verify mock expectations
		mockRepo.AssertExpectations(t)
	})

	t.Run("user already exists by phone", func(t *testing.T) {
		// Arrange
		req := &domain.RegisterRequest{
			Email:    "<EMAIL>",
			Phone:    "+1234567890",
			Password: "password123",
		}

		existingUser := &domain.User{
			ID:    uuid.New(),
			Phone: req.Phone,
		}

		// Mock expectations
		mockRepo.On("GetUserByEmail", req.Email).Return(nil, errors.ErrUserNotFound)
		mockRepo.On("GetUserByPhone", req.Phone).Return(existingUser, nil)

		// Act
		response, err := service.Register(req)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, errors.ErrUserAlreadyExists, err)

		// Verify mock expectations
		mockRepo.AssertExpectations(t)
	})
}

func TestAuthService_Login(t *testing.T) {
	// Setup
	mockRepo := new(MockRepository)
	cfg := &config.Config{
		JWT: config.JWTConfig{
			AccessSecret:     "test-access-secret",
			RefreshSecret:    "test-refresh-secret",
			AccessExpiryHrs:  1,
			RefreshExpiryHrs: 168,
		},
	}
	jwtService := auth.NewJWTService(cfg)
	service := NewService(mockRepo, jwtService)

	t.Run("successful login", func(t *testing.T) {
		// Arrange
		req := &domain.LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}

		// Create a user with hashed password
		hashedPassword := "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi" // "password123"
		user := &domain.User{
			ID:         uuid.New(),
			Email:      req.Email,
			Phone:      "+1234567890",
			Password:   hashedPassword,
			IsVerified: true,
			CreatedAt:  time.Now(),
		}

		// Mock expectations
		mockRepo.On("GetUserByEmail", req.Email).Return(user, nil)
		mockRepo.On("CreateLoginLog", mock.AnythingOfType("*domain.LoginLog")).Return(nil)

		// Act
		response, err := service.Login(req, "127.0.0.1", "test-device")

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.NotEmpty(t, response.AccessToken)
		assert.NotEmpty(t, response.RefreshToken)
		assert.Equal(t, user.Email, response.User.Email)
		assert.Equal(t, user.Phone, response.User.Phone)

		// Verify mock expectations
		mockRepo.AssertExpectations(t)
	})

	t.Run("user not found", func(t *testing.T) {
		// Arrange
		req := &domain.LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}

		// Mock expectations
		mockRepo.On("GetUserByEmail", req.Email).Return(nil, errors.ErrUserNotFound)

		// Act
		response, err := service.Login(req, "127.0.0.1", "test-device")

		// Assert
		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, errors.ErrInvalidCredentials, err)

		// Verify mock expectations
		mockRepo.AssertExpectations(t)
	})
}
