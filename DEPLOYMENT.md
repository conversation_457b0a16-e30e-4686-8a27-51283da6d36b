# B2C Core Service - Deployment Guide

## 🚀 Quick Start Commands

### 1. Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd b2c-core-service

# Setup environment
make setup
# Edit .env file with your configuration

# Install development tools
make install-tools
```

### 2. Local Development
```bash
# Option 1: Full Docker environment
make docker-up

# Option 2: Database in Docker, API locally
make db-up
make run

# Option 3: Everything local (requires local PostgreSQL)
make run
```

### 3. Access Points
- **API**: http://localhost:8080
- **Health Check**: http://localhost:8080/health
- **API Docs**: http://localhost:8080/docs/index.html
- **pgAdmin**: http://localhost:5050 (<EMAIL> / admin123)
- **Gateway**: http://localhost (NGINX proxy)

## 🧪 Testing the API

### 1. Health Check
```bash
curl http://localhost:8080/health
```

### 2. Register a User
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "phone": "+**********",
    "password": "password123"
  }'
```

### 3. Login
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 4. Get Profile (use token from login response)
```bash
curl -X GET http://localhost:8080/api/v1/auth/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🐳 Docker Deployment

### Development Environment
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f b2c-api

# Stop services
docker-compose down
```

### Production Build
```bash
# Build production image
docker build -t b2c-core-service:prod .

# Run production container
docker run -d \
  --name b2c-api \
  -p 8080:8080 \
  --env-file .env.production \
  b2c-core-service:prod
```

## ☸️ Kubernetes Deployment

### Prerequisites
- Kubernetes cluster
- Helm 3.x
- kubectl configured

### 1. Create Secrets
```bash
# Database secret
kubectl create secret generic b2c-db-secret \
  --from-literal=password=your-db-password \
  --namespace b2c

# JWT secrets
kubectl create secret generic b2c-jwt-secret \
  --from-literal=access-secret=your-access-secret \
  --from-literal=refresh-secret=your-refresh-secret \
  --namespace b2c
```

### 2. Deploy with Helm
```bash
# Add dependencies
helm dependency update deploy/helm/

# Install
helm install b2c-core-service deploy/helm/ \
  --namespace b2c \
  --create-namespace \
  --values deploy/helm/values.yaml

# Check status
kubectl get pods -n b2c
kubectl get services -n b2c
```

### 3. Access the Application
```bash
# Port forward for testing
kubectl port-forward service/b2c-core-service 8080:8080 -n b2c

# Or configure ingress for external access
```

## 🔧 Configuration

### Environment Variables
```bash
# Server
SERVER_PORT=8080
ENVIRONMENT=production

# Database
DB_HOST=your-db-host
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your-password
DB_NAME=db_b2c
DB_SCHEMA=cashandcarry
DB_SSLMODE=require

# JWT
JWT_ACCESS_SECRET=your-super-secret-access-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_ACCESS_EXPIRY_HRS=1
JWT_REFRESH_EXPIRY_HRS=168
```

### Database Setup
```sql
-- Create database
CREATE DATABASE db_b2c;

-- Create schema
CREATE SCHEMA cashandcarry;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

## 📊 Monitoring

### Health Checks
```bash
# Application health
curl http://localhost:8080/health

# Database connectivity is included in health check
```

### Logs
```bash
# Docker logs
docker-compose logs -f b2c-api

# Kubernetes logs
kubectl logs -f deployment/b2c-core-service -n b2c
```

## 🔒 Security Checklist

### Production Deployment
- [ ] Use strong JWT secrets (32+ characters)
- [ ] Enable SSL/TLS
- [ ] Configure proper CORS origins
- [ ] Use Kubernetes secrets for sensitive data
- [ ] Enable database SSL
- [ ] Configure rate limiting
- [ ] Set up monitoring and alerting
- [ ] Regular security updates

### Database Security
- [ ] Use strong database passwords
- [ ] Enable SSL connections
- [ ] Restrict database access
- [ ] Regular backups
- [ ] Monitor database logs

## 🚨 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check database status
docker-compose ps postgres

# Check logs
docker-compose logs postgres

# Verify connection
docker-compose exec postgres psql -U postgres -d db_b2c -c "SELECT 1;"
```

#### 2. Application Won't Start
```bash
# Check logs
docker-compose logs b2c-api

# Verify environment variables
docker-compose exec b2c-api env | grep DB_

# Check health endpoint
curl http://localhost:8080/health
```

#### 3. JWT Token Issues
- Verify JWT secrets are set correctly
- Check token expiry times
- Ensure proper Authorization header format: `Bearer <token>`

#### 4. Database Migration Issues
```bash
# Reset database (WARNING: deletes all data)
make db-reset

# Check database schema
docker-compose exec postgres psql -U postgres -d db_b2c -c "\dt cashandcarry.*"
```

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale with Docker Compose
docker-compose up -d --scale b2c-api=3

# Scale with Kubernetes
kubectl scale deployment b2c-core-service --replicas=5 -n b2c
```

### Performance Optimization
- Enable database connection pooling
- Configure Redis for caching
- Use CDN for static assets
- Implement database read replicas
- Monitor and optimize slow queries

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy B2C Core Service
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Build and push Docker image
        run: |
          docker build -t b2c-core-service:${{ github.sha }} .
          docker push b2c-core-service:${{ github.sha }}
      - name: Deploy to Kubernetes
        run: |
          helm upgrade b2c-core-service deploy/helm/ \
            --set image.tag=${{ github.sha }} \
            --namespace production
```

## 📞 Support

For issues and questions:
1. Check the logs first
2. Verify configuration
3. Test with curl commands
4. Check database connectivity
5. Review this deployment guide
6. Create an issue in the repository
