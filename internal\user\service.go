package user

import (
	"time"

	"b2c-core-service/domain"
	"b2c-core-service/internal/common/errors"

	"github.com/google/uuid"
)

// Service interface defines user service methods
type Service interface {
	// Profile operations
	CreateProfile(userID uuid.UUID, req *domain.CreateProfileRequest) (*domain.ProfileDTO, error)
	GetProfile(userID uuid.UUID) (*domain.ProfileDTO, error)
	UpdateProfile(userID uuid.UUID, req *domain.CreateProfileRequest) (*domain.ProfileDTO, error)
	DeleteProfile(userID uuid.UUID) error

	// Address operations
	CreateAddress(userID uuid.UUID, req *domain.CreateAddressRequest) (*domain.AddressDTO, error)
	GetAddresses(userID uuid.UUID) ([]*domain.AddressDTO, error)
	GetAddress(userID, addressID uuid.UUID) (*domain.AddressDTO, error)
	UpdateAddress(userID, addressID uuid.UUID, req *domain.CreateAddressRequest) (*domain.AddressDTO, error)
	DeleteAddress(userID, addressID uuid.UUID) error
	SetDefaultAddress(userID, addressID uuid.UUID) error
}

// service implements Service interface
type service struct {
	repo Repository
}

// NewService creates a new user service
func NewService(repo Repository) Service {
	return &service{repo: repo}
}

// Profile operations

// CreateProfile creates a new user profile
func (s *service) CreateProfile(userID uuid.UUID, req *domain.CreateProfileRequest) (*domain.ProfileDTO, error) {
	// Check if profile already exists
	if _, err := s.repo.GetProfileByUserID(userID); err == nil {
		return nil, errors.NewConflictError("Profile already exists for this user")
	}

	profile := &domain.Profile{
		ID:        uuid.New(),
		UserID:    userID,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Gender:    req.Gender,
		DOB:       req.DOB,
		Language:  req.Language,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if profile.Language == "" {
		profile.Language = "en" // Default language
	}

	if err := s.repo.CreateProfile(profile); err != nil {
		return nil, err
	}

	return &domain.ProfileDTO{
		ID:        profile.ID,
		UserID:    profile.UserID,
		FirstName: profile.FirstName,
		LastName:  profile.LastName,
		Gender:    profile.Gender,
		DOB:       profile.DOB,
		Language:  profile.Language,
	}, nil
}

// GetProfile retrieves a user profile
func (s *service) GetProfile(userID uuid.UUID) (*domain.ProfileDTO, error) {
	profile, err := s.repo.GetProfileByUserID(userID)
	if err != nil {
		return nil, err
	}

	return &domain.ProfileDTO{
		ID:        profile.ID,
		UserID:    profile.UserID,
		FirstName: profile.FirstName,
		LastName:  profile.LastName,
		Gender:    profile.Gender,
		DOB:       profile.DOB,
		Language:  profile.Language,
	}, nil
}

// UpdateProfile updates a user profile
func (s *service) UpdateProfile(userID uuid.UUID, req *domain.CreateProfileRequest) (*domain.ProfileDTO, error) {
	profile, err := s.repo.GetProfileByUserID(userID)
	if err != nil {
		return nil, err
	}

	// Update fields
	profile.FirstName = req.FirstName
	profile.LastName = req.LastName
	profile.Gender = req.Gender
	profile.DOB = req.DOB
	if req.Language != "" {
		profile.Language = req.Language
	}
	profile.UpdatedAt = time.Now()

	if err := s.repo.UpdateProfile(profile); err != nil {
		return nil, err
	}

	return &domain.ProfileDTO{
		ID:        profile.ID,
		UserID:    profile.UserID,
		FirstName: profile.FirstName,
		LastName:  profile.LastName,
		Gender:    profile.Gender,
		DOB:       profile.DOB,
		Language:  profile.Language,
	}, nil
}

// DeleteProfile deletes a user profile
func (s *service) DeleteProfile(userID uuid.UUID) error {
	// Check if profile exists
	if _, err := s.repo.GetProfileByUserID(userID); err != nil {
		return err
	}

	return s.repo.DeleteProfile(userID)
}

// Address operations

// CreateAddress creates a new address
func (s *service) CreateAddress(userID uuid.UUID, req *domain.CreateAddressRequest) (*domain.AddressDTO, error) {
	address := &domain.Address{
		ID:        uuid.New(),
		UserID:    userID,
		Line1:     req.Line1,
		Line2:     req.Line2,
		City:      req.City,
		State:     req.State,
		Pincode:   req.Pincode,
		Location:  req.Location,
		Type:      req.Type,
		IsDefault: req.IsDefault,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.repo.CreateAddress(address); err != nil {
		return nil, err
	}

	return &domain.AddressDTO{
		ID:        address.ID,
		UserID:    address.UserID,
		Line1:     address.Line1,
		Line2:     address.Line2,
		City:      address.City,
		State:     address.State,
		Pincode:   address.Pincode,
		Location:  address.Location,
		Type:      address.Type,
		IsDefault: address.IsDefault,
	}, nil
}

// GetAddresses retrieves all addresses for a user
func (s *service) GetAddresses(userID uuid.UUID) ([]*domain.AddressDTO, error) {
	addresses, err := s.repo.GetAddressesByUserID(userID)
	if err != nil {
		return nil, err
	}

	var addressDTOs []*domain.AddressDTO
	for _, addr := range addresses {
		addressDTOs = append(addressDTOs, &domain.AddressDTO{
			ID:        addr.ID,
			UserID:    addr.UserID,
			Line1:     addr.Line1,
			Line2:     addr.Line2,
			City:      addr.City,
			State:     addr.State,
			Pincode:   addr.Pincode,
			Location:  addr.Location,
			Type:      addr.Type,
			IsDefault: addr.IsDefault,
		})
	}

	return addressDTOs, nil
}

// GetAddress retrieves a specific address
func (s *service) GetAddress(userID, addressID uuid.UUID) (*domain.AddressDTO, error) {
	address, err := s.repo.GetAddressByID(addressID)
	if err != nil {
		return nil, err
	}

	// Verify the address belongs to the user
	if address.UserID != userID {
		return nil, errors.ErrForbidden
	}

	return &domain.AddressDTO{
		ID:        address.ID,
		UserID:    address.UserID,
		Line1:     address.Line1,
		Line2:     address.Line2,
		City:      address.City,
		State:     address.State,
		Pincode:   address.Pincode,
		Location:  address.Location,
		Type:      address.Type,
		IsDefault: address.IsDefault,
	}, nil
}

// UpdateAddress updates an address
func (s *service) UpdateAddress(userID, addressID uuid.UUID, req *domain.CreateAddressRequest) (*domain.AddressDTO, error) {
	address, err := s.repo.GetAddressByID(addressID)
	if err != nil {
		return nil, err
	}

	// Verify the address belongs to the user
	if address.UserID != userID {
		return nil, errors.ErrForbidden
	}

	// Update fields
	address.Line1 = req.Line1
	address.Line2 = req.Line2
	address.City = req.City
	address.State = req.State
	address.Pincode = req.Pincode
	address.Location = req.Location
	address.Type = req.Type
	address.IsDefault = req.IsDefault
	address.UpdatedAt = time.Now()

	if err := s.repo.UpdateAddress(address); err != nil {
		return nil, err
	}

	return &domain.AddressDTO{
		ID:        address.ID,
		UserID:    address.UserID,
		Line1:     address.Line1,
		Line2:     address.Line2,
		City:      address.City,
		State:     address.State,
		Pincode:   address.Pincode,
		Location:  address.Location,
		Type:      address.Type,
		IsDefault: address.IsDefault,
	}, nil
}

// DeleteAddress deletes an address
func (s *service) DeleteAddress(userID, addressID uuid.UUID) error {
	address, err := s.repo.GetAddressByID(addressID)
	if err != nil {
		return err
	}

	// Verify the address belongs to the user
	if address.UserID != userID {
		return errors.ErrForbidden
	}

	return s.repo.DeleteAddress(addressID)
}

// SetDefaultAddress sets an address as default
func (s *service) SetDefaultAddress(userID, addressID uuid.UUID) error {
	address, err := s.repo.GetAddressByID(addressID)
	if err != nil {
		return err
	}

	// Verify the address belongs to the user
	if address.UserID != userID {
		return errors.ErrForbidden
	}

	return s.repo.SetDefaultAddress(userID, addressID)
}
