package user

import (
	"net/http"

	"b2c-core-service/domain"
	"b2c-core-service/internal/common/errors"
	"b2c-core-service/internal/common/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// <PERSON><PERSON> handles user HTTP requests
type Handler struct {
	service Service
}

// NewHandler creates a new user handler
func NewHandler(service Service) *Handler {
	return &Handler{service: service}
}

// Profile handlers

// CreateProfile handles profile creation
// @Summary Create user profile
// @Description Create a new profile for the authenticated user
// @Tags user
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body domain.CreateProfileRequest true "Profile creation request"
// @Success 201 {object} domain.APIResponse{data=domain.ProfileDTO}
// @Failure 400 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Failure 409 {object} domain.APIResponse
// @Router /user/profile [post]
func (h *<PERSON><PERSON>) CreateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	var req domain.CreateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError(err.Error()))
		return
	}

	profile, err := h.service.CreateProfile(userID.(uuid.UUID), &req)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusCreated, domain.APIResponse{
		Success: true,
		Message: "Profile created successfully",
		Data:    profile,
	})
}

// GetProfile handles profile retrieval
// @Summary Get user profile
// @Description Get the profile of the authenticated user
// @Tags user
// @Produce json
// @Security BearerAuth
// @Success 200 {object} domain.APIResponse{data=domain.ProfileDTO}
// @Failure 401 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /user/profile [get]
func (h *Handler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	profile, err := h.service.GetProfile(userID.(uuid.UUID))
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Profile retrieved successfully",
		Data:    profile,
	})
}

// UpdateProfile handles profile updates
// @Summary Update user profile
// @Description Update the profile of the authenticated user
// @Tags user
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body domain.CreateProfileRequest true "Profile update request"
// @Success 200 {object} domain.APIResponse{data=domain.ProfileDTO}
// @Failure 400 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /user/profile [put]
func (h *Handler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	var req domain.CreateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError(err.Error()))
		return
	}

	profile, err := h.service.UpdateProfile(userID.(uuid.UUID), &req)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Profile updated successfully",
		Data:    profile,
	})
}

// DeleteProfile handles profile deletion
// @Summary Delete user profile
// @Description Delete the profile of the authenticated user
// @Tags user
// @Produce json
// @Security BearerAuth
// @Success 200 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /user/profile [delete]
func (h *Handler) DeleteProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	err := h.service.DeleteProfile(userID.(uuid.UUID))
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Profile deleted successfully",
	})
}

// Address handlers

// CreateAddress handles address creation
// @Summary Create user address
// @Description Create a new address for the authenticated user
// @Tags user
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body domain.CreateAddressRequest true "Address creation request"
// @Success 201 {object} domain.APIResponse{data=domain.AddressDTO}
// @Failure 400 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Router /user/addresses [post]
func (h *Handler) CreateAddress(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	var req domain.CreateAddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError(err.Error()))
		return
	}

	address, err := h.service.CreateAddress(userID.(uuid.UUID), &req)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusCreated, domain.APIResponse{
		Success: true,
		Message: "Address created successfully",
		Data:    address,
	})
}

// GetAddresses handles address list retrieval
// @Summary Get user addresses
// @Description Get all addresses of the authenticated user
// @Tags user
// @Produce json
// @Security BearerAuth
// @Success 200 {object} domain.APIResponse{data=[]domain.AddressDTO}
// @Failure 401 {object} domain.APIResponse
// @Router /user/addresses [get]
func (h *Handler) GetAddresses(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	addresses, err := h.service.GetAddresses(userID.(uuid.UUID))
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Addresses retrieved successfully",
		Data:    addresses,
	})
}

// GetAddress handles single address retrieval
// @Summary Get user address
// @Description Get a specific address of the authenticated user
// @Tags user
// @Produce json
// @Security BearerAuth
// @Param id path string true "Address ID"
// @Success 200 {object} domain.APIResponse{data=domain.AddressDTO}
// @Failure 401 {object} domain.APIResponse
// @Failure 403 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /user/addresses/{id} [get]
func (h *Handler) GetAddress(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	addressIDStr := c.Param("id")
	addressID, err := uuid.Parse(addressIDStr)
	if err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError("Invalid address ID"))
		return
	}

	address, err := h.service.GetAddress(userID.(uuid.UUID), addressID)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Address retrieved successfully",
		Data:    address,
	})
}

// UpdateAddress handles address updates
// @Summary Update user address
// @Description Update a specific address of the authenticated user
// @Tags user
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Address ID"
// @Param request body domain.CreateAddressRequest true "Address update request"
// @Success 200 {object} domain.APIResponse{data=domain.AddressDTO}
// @Failure 400 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Failure 403 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /user/addresses/{id} [put]
func (h *Handler) UpdateAddress(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	addressIDStr := c.Param("id")
	addressID, err := uuid.Parse(addressIDStr)
	if err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError("Invalid address ID"))
		return
	}

	var req domain.CreateAddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError(err.Error()))
		return
	}

	address, err := h.service.UpdateAddress(userID.(uuid.UUID), addressID, &req)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Address updated successfully",
		Data:    address,
	})
}

// DeleteAddress handles address deletion
// @Summary Delete user address
// @Description Delete a specific address of the authenticated user
// @Tags user
// @Produce json
// @Security BearerAuth
// @Param id path string true "Address ID"
// @Success 200 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Failure 403 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /user/addresses/{id} [delete]
func (h *Handler) DeleteAddress(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	addressIDStr := c.Param("id")
	addressID, err := uuid.Parse(addressIDStr)
	if err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError("Invalid address ID"))
		return
	}

	err = h.service.DeleteAddress(userID.(uuid.UUID), addressID)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Address deleted successfully",
	})
}

// SetDefaultAddress handles setting default address
// @Summary Set default address
// @Description Set a specific address as the default address for the authenticated user
// @Tags user
// @Produce json
// @Security BearerAuth
// @Param id path string true "Address ID"
// @Success 200 {object} domain.APIResponse
// @Failure 401 {object} domain.APIResponse
// @Failure 403 {object} domain.APIResponse
// @Failure 404 {object} domain.APIResponse
// @Router /user/addresses/{id}/default [patch]
func (h *Handler) SetDefaultAddress(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		middlewares.AbortWithError(c, errors.ErrUnauthorized)
		return
	}

	addressIDStr := c.Param("id")
	addressID, err := uuid.Parse(addressIDStr)
	if err != nil {
		middlewares.AbortWithError(c, errors.NewValidationError("Invalid address ID"))
		return
	}

	err = h.service.SetDefaultAddress(userID.(uuid.UUID), addressID)
	if err != nil {
		if appErr, ok := errors.IsAppError(err); ok {
			middlewares.AbortWithError(c, appErr)
		} else {
			middlewares.AbortWithInternalError(c, err)
		}
		return
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Message: "Default address set successfully",
	})
}
