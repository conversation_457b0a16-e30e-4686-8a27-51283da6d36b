package user

import (
	"b2c-core-service/domain"
	"b2c-core-service/internal/common/errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Repository interface defines user repository methods
type Repository interface {
	// Profile operations
	CreateProfile(profile *domain.Profile) error
	GetProfileByUserID(userID uuid.UUID) (*domain.Profile, error)
	UpdateProfile(profile *domain.Profile) error
	DeleteProfile(userID uuid.UUID) error

	// Address operations
	CreateAddress(address *domain.Address) error
	GetAddressesByUserID(userID uuid.UUID) ([]*domain.Address, error)
	GetAddressByID(id uuid.UUID) (*domain.Address, error)
	UpdateAddress(address *domain.Address) error
	DeleteAddress(id uuid.UUID) error
	SetDefaultAddress(userID, addressID uuid.UUID) error
}

// repository implements Repository interface
type repository struct {
	db *gorm.DB
}

// NewRepository creates a new user repository
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

// Profile operations

// CreateProfile creates a new user profile
func (r *repository) CreateProfile(profile *domain.Profile) error {
	if err := r.db.Create(profile).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// GetProfileByUserID retrieves a profile by user ID
func (r *repository) GetProfileByUserID(userID uuid.UUID) (*domain.Profile, error) {
	var profile domain.Profile
	if err := r.db.Where("user_id = ?", userID).First(&profile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("Profile")
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &profile, nil
}

// UpdateProfile updates a user profile
func (r *repository) UpdateProfile(profile *domain.Profile) error {
	if err := r.db.Save(profile).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// DeleteProfile deletes a user profile
func (r *repository) DeleteProfile(userID uuid.UUID) error {
	if err := r.db.Where("user_id = ?", userID).Delete(&domain.Profile{}).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// Address operations

// CreateAddress creates a new address
func (r *repository) CreateAddress(address *domain.Address) error {
	// Start transaction
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// If this is set as default, unset other default addresses
	if address.IsDefault {
		if err := tx.Model(&domain.Address{}).
			Where("user_id = ? AND is_default = ?", address.UserID, true).
			Update("is_default", false).Error; err != nil {
			tx.Rollback()
			return errors.NewDatabaseError(err.Error())
		}
	}

	// Create the new address
	if err := tx.Create(address).Error; err != nil {
		tx.Rollback()
		return errors.NewDatabaseError(err.Error())
	}

	return tx.Commit().Error
}

// GetAddressesByUserID retrieves all addresses for a user
func (r *repository) GetAddressesByUserID(userID uuid.UUID) ([]*domain.Address, error) {
	var addresses []*domain.Address
	if err := r.db.Where("user_id = ?", userID).Order("is_default DESC, created_at DESC").Find(&addresses).Error; err != nil {
		return nil, errors.NewDatabaseError(err.Error())
	}
	return addresses, nil
}

// GetAddressByID retrieves an address by ID
func (r *repository) GetAddressByID(id uuid.UUID) (*domain.Address, error) {
	var address domain.Address
	if err := r.db.Where("id = ?", id).First(&address).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("Address")
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &address, nil
}

// UpdateAddress updates an address
func (r *repository) UpdateAddress(address *domain.Address) error {
	// Start transaction
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// If this is set as default, unset other default addresses
	if address.IsDefault {
		if err := tx.Model(&domain.Address{}).
			Where("user_id = ? AND id != ? AND is_default = ?", address.UserID, address.ID, true).
			Update("is_default", false).Error; err != nil {
			tx.Rollback()
			return errors.NewDatabaseError(err.Error())
		}
	}

	// Update the address
	if err := tx.Save(address).Error; err != nil {
		tx.Rollback()
		return errors.NewDatabaseError(err.Error())
	}

	return tx.Commit().Error
}

// DeleteAddress deletes an address
func (r *repository) DeleteAddress(id uuid.UUID) error {
	if err := r.db.Delete(&domain.Address{}, id).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// SetDefaultAddress sets an address as default
func (r *repository) SetDefaultAddress(userID, addressID uuid.UUID) error {
	// Start transaction
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Unset all default addresses for the user
	if err := tx.Model(&domain.Address{}).
		Where("user_id = ?", userID).
		Update("is_default", false).Error; err != nil {
		tx.Rollback()
		return errors.NewDatabaseError(err.Error())
	}

	// Set the specified address as default
	if err := tx.Model(&domain.Address{}).
		Where("id = ? AND user_id = ?", addressID, userID).
		Update("is_default", true).Error; err != nil {
		tx.Rollback()
		return errors.NewDatabaseError(err.Error())
	}

	return tx.Commit().Error
}
