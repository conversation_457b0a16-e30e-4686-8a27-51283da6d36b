package errors

import (
	"fmt"
	"net/http"
)

// AppError represents a custom application error
type AppError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Status  int    `json:"status"`
	Details string `json:"details,omitempty"`
}

func (e *AppError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Error codes
const (
	// Authentication errors
	ErrCodeUnauthorized     = "UNAUTHORIZED"
	ErrCodeInvalidToken     = "INVALID_TOKEN"
	ErrCodeTokenExpired     = "TOKEN_EXPIRED"
	ErrCodeInvalidCredentials = "INVALID_CREDENTIALS"
	ErrCodeUserNotFound     = "USER_NOT_FOUND"
	ErrCodeUserAlreadyExists = "USER_ALREADY_EXISTS"

	// Validation errors
	ErrCodeValidationFailed = "VALIDATION_FAILED"
	ErrCodeInvalidInput     = "INVALID_INPUT"
	ErrCodeMissingField     = "MISSING_FIELD"

	// Resource errors
	ErrCodeNotFound         = "NOT_FOUND"
	ErrCodeAlreadyExists    = "ALREADY_EXISTS"
	ErrCodeConflict         = "CONFLICT"
	ErrCodeForbidden        = "FORBIDDEN"

	// Business logic errors
	ErrCodeInsufficientStock = "INSUFFICIENT_STOCK"
	ErrCodeInvalidQuantity   = "INVALID_QUANTITY"
	ErrCodeOrderNotFound     = "ORDER_NOT_FOUND"
	ErrCodeCartEmpty         = "CART_EMPTY"
	ErrCodeInvalidCoupon     = "INVALID_COUPON"
	ErrCodeCouponExpired     = "COUPON_EXPIRED"

	// System errors
	ErrCodeInternalServer   = "INTERNAL_SERVER_ERROR"
	ErrCodeDatabaseError    = "DATABASE_ERROR"
	ErrCodeExternalService  = "EXTERNAL_SERVICE_ERROR"
)

// Predefined errors
var (
	ErrUnauthorized = &AppError{
		Code:    ErrCodeUnauthorized,
		Message: "Unauthorized access",
		Status:  http.StatusUnauthorized,
	}

	ErrInvalidToken = &AppError{
		Code:    ErrCodeInvalidToken,
		Message: "Invalid or malformed token",
		Status:  http.StatusUnauthorized,
	}

	ErrTokenExpired = &AppError{
		Code:    ErrCodeTokenExpired,
		Message: "Token has expired",
		Status:  http.StatusUnauthorized,
	}

	ErrInvalidCredentials = &AppError{
		Code:    ErrCodeInvalidCredentials,
		Message: "Invalid email or password",
		Status:  http.StatusUnauthorized,
	}

	ErrUserNotFound = &AppError{
		Code:    ErrCodeUserNotFound,
		Message: "User not found",
		Status:  http.StatusNotFound,
	}

	ErrUserAlreadyExists = &AppError{
		Code:    ErrCodeUserAlreadyExists,
		Message: "User already exists with this email or phone",
		Status:  http.StatusConflict,
	}

	ErrValidationFailed = &AppError{
		Code:    ErrCodeValidationFailed,
		Message: "Validation failed",
		Status:  http.StatusBadRequest,
	}

	ErrNotFound = &AppError{
		Code:    ErrCodeNotFound,
		Message: "Resource not found",
		Status:  http.StatusNotFound,
	}

	ErrForbidden = &AppError{
		Code:    ErrCodeForbidden,
		Message: "Access forbidden",
		Status:  http.StatusForbidden,
	}

	ErrInternalServer = &AppError{
		Code:    ErrCodeInternalServer,
		Message: "Internal server error",
		Status:  http.StatusInternalServerError,
	}

	ErrDatabaseError = &AppError{
		Code:    ErrCodeDatabaseError,
		Message: "Database operation failed",
		Status:  http.StatusInternalServerError,
	}

	ErrInsufficientStock = &AppError{
		Code:    ErrCodeInsufficientStock,
		Message: "Insufficient stock available",
		Status:  http.StatusBadRequest,
	}

	ErrCartEmpty = &AppError{
		Code:    ErrCodeCartEmpty,
		Message: "Cart is empty",
		Status:  http.StatusBadRequest,
	}
)

// Constructor functions for dynamic errors
func NewValidationError(message string) *AppError {
	return &AppError{
		Code:    ErrCodeValidationFailed,
		Message: message,
		Status:  http.StatusBadRequest,
	}
}

func NewNotFoundError(resource string) *AppError {
	return &AppError{
		Code:    ErrCodeNotFound,
		Message: fmt.Sprintf("%s not found", resource),
		Status:  http.StatusNotFound,
	}
}

func NewConflictError(message string) *AppError {
	return &AppError{
		Code:    ErrCodeConflict,
		Message: message,
		Status:  http.StatusConflict,
	}
}

func NewInternalError(message string) *AppError {
	return &AppError{
		Code:    ErrCodeInternalServer,
		Message: message,
		Status:  http.StatusInternalServerError,
	}
}

func NewDatabaseError(details string) *AppError {
	return &AppError{
		Code:    ErrCodeDatabaseError,
		Message: "Database operation failed",
		Status:  http.StatusInternalServerError,
		Details: details,
	}
}

func NewBusinessError(code, message string) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Status:  http.StatusBadRequest,
	}
}

// IsAppError checks if an error is an AppError
func IsAppError(err error) (*AppError, bool) {
	if appErr, ok := err.(*AppError); ok {
		return appErr, true
	}
	return nil, false
}

// WrapError wraps a generic error into an AppError
func WrapError(err error, code, message string) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Status:  http.StatusInternalServerError,
		Details: err.Error(),
	}
}
