package product

import (
	"b2c-core-service/domain"
	"b2c-core-service/internal/common/errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Repository interface defines product repository methods
type Repository interface {
	// Category operations
	CreateCategory(category *domain.ProductCategory) error
	GetCategories() ([]*domain.ProductCategory, error)
	GetCategoryByID(id uuid.UUID) (*domain.ProductCategory, error)
	UpdateCategory(category *domain.ProductCategory) error
	DeleteCategory(id uuid.UUID) error

	// Product operations
	CreateProduct(product *domain.Product) error
	GetProducts(filters *ProductFilters) ([]*domain.Product, int64, error)
	GetProductByID(id uuid.UUID) (*domain.Product, error)
	GetProductBySlug(slug string) (*domain.Product, error)
	UpdateProduct(product *domain.Product) error
	DeleteProduct(id uuid.UUID) error
	UpdateStock(productID uuid.UUID, quantity int) error

	// Product detail operations
	CreateProductDetail(detail *domain.ProductDetail) error
	GetProductDetailByProductID(productID uuid.UUID) (*domain.ProductDetail, error)
	UpdateProductDetail(detail *domain.ProductDetail) error

	// Shop operations
	CreateShop(shop *domain.Shop) error
	GetShops(pincode string) ([]*domain.Shop, error)
	GetShopByID(id uuid.UUID) (*domain.Shop, error)
	UpdateShop(shop *domain.Shop) error
	DeleteShop(id uuid.UUID) error
}

// ProductFilters represents filters for product search
type ProductFilters struct {
	CategoryID uuid.UUID
	ShopID     uuid.UUID
	MinPrice   float64
	MaxPrice   float64
	Pincode    string
	Query      string
	Page       int
	Limit      int
}

// repository implements Repository interface
type repository struct {
	db *gorm.DB
}

// NewRepository creates a new product repository
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

// Category operations

// CreateCategory creates a new product category
func (r *repository) CreateCategory(category *domain.ProductCategory) error {
	if err := r.db.Create(category).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// GetCategories retrieves all product categories
func (r *repository) GetCategories() ([]*domain.ProductCategory, error) {
	var categories []*domain.ProductCategory
	if err := r.db.Order("name").Find(&categories).Error; err != nil {
		return nil, errors.NewDatabaseError(err.Error())
	}
	return categories, nil
}

// GetCategoryByID retrieves a category by ID
func (r *repository) GetCategoryByID(id uuid.UUID) (*domain.ProductCategory, error) {
	var category domain.ProductCategory
	if err := r.db.Where("id = ?", id).First(&category).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("Category")
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &category, nil
}

// UpdateCategory updates a product category
func (r *repository) UpdateCategory(category *domain.ProductCategory) error {
	if err := r.db.Save(category).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// DeleteCategory deletes a product category
func (r *repository) DeleteCategory(id uuid.UUID) error {
	if err := r.db.Delete(&domain.ProductCategory{}, id).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// Product operations

// CreateProduct creates a new product
func (r *repository) CreateProduct(product *domain.Product) error {
	if err := r.db.Create(product).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// GetProducts retrieves products with filters and pagination
func (r *repository) GetProducts(filters *ProductFilters) ([]*domain.Product, int64, error) {
	query := r.db.Model(&domain.Product{}).Where("is_available = ?", true)

	// Apply filters
	if filters.CategoryID != uuid.Nil {
		query = query.Where("category_id = ?", filters.CategoryID)
	}
	if filters.ShopID != uuid.Nil {
		query = query.Where("shop_id = ?", filters.ShopID)
	}
	if filters.MinPrice > 0 {
		query = query.Where("price >= ?", filters.MinPrice)
	}
	if filters.MaxPrice > 0 {
		query = query.Where("price <= ?", filters.MaxPrice)
	}
	if filters.Query != "" {
		query = query.Where("name ILIKE ?", "%"+filters.Query+"%")
	}
	if filters.Pincode != "" {
		// Join with shops to filter by pincode
		query = query.Joins("JOIN shops ON products.shop_id = shops.id").
			Where("shops.pincode = ?", filters.Pincode)
	}

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errors.NewDatabaseError(err.Error())
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Retrieve products
	var products []*domain.Product
	if err := query.Order("created_at DESC").Find(&products).Error; err != nil {
		return nil, 0, errors.NewDatabaseError(err.Error())
	}

	return products, total, nil
}

// GetProductByID retrieves a product by ID
func (r *repository) GetProductByID(id uuid.UUID) (*domain.Product, error) {
	var product domain.Product
	if err := r.db.Where("id = ?", id).First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("Product")
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &product, nil
}

// GetProductBySlug retrieves a product by slug
func (r *repository) GetProductBySlug(slug string) (*domain.Product, error) {
	var product domain.Product
	if err := r.db.Where("slug = ?", slug).First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("Product")
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &product, nil
}

// UpdateProduct updates a product
func (r *repository) UpdateProduct(product *domain.Product) error {
	if err := r.db.Save(product).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// DeleteProduct soft deletes a product
func (r *repository) DeleteProduct(id uuid.UUID) error {
	if err := r.db.Delete(&domain.Product{}, id).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// UpdateStock updates product stock quantity
func (r *repository) UpdateStock(productID uuid.UUID, quantity int) error {
	if err := r.db.Model(&domain.Product{}).
		Where("id = ?", productID).
		Update("stock_qty", quantity).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// Product detail operations

// CreateProductDetail creates product detail
func (r *repository) CreateProductDetail(detail *domain.ProductDetail) error {
	if err := r.db.Create(detail).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// GetProductDetailByProductID retrieves product detail by product ID
func (r *repository) GetProductDetailByProductID(productID uuid.UUID) (*domain.ProductDetail, error) {
	var detail domain.ProductDetail
	if err := r.db.Where("product_id = ?", productID).First(&detail).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("Product Detail")
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &detail, nil
}

// UpdateProductDetail updates product detail
func (r *repository) UpdateProductDetail(detail *domain.ProductDetail) error {
	if err := r.db.Save(detail).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// Shop operations

// CreateShop creates a new shop
func (r *repository) CreateShop(shop *domain.Shop) error {
	if err := r.db.Create(shop).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// GetShops retrieves shops by pincode
func (r *repository) GetShops(pincode string) ([]*domain.Shop, error) {
	var shops []*domain.Shop
	query := r.db.Where("is_active = ?", true)
	
	if pincode != "" {
		query = query.Where("pincode = ?", pincode)
	}
	
	if err := query.Order("rating DESC").Find(&shops).Error; err != nil {
		return nil, errors.NewDatabaseError(err.Error())
	}
	return shops, nil
}

// GetShopByID retrieves a shop by ID
func (r *repository) GetShopByID(id uuid.UUID) (*domain.Shop, error) {
	var shop domain.Shop
	if err := r.db.Where("id = ?", id).First(&shop).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("Shop")
		}
		return nil, errors.NewDatabaseError(err.Error())
	}
	return &shop, nil
}

// UpdateShop updates a shop
func (r *repository) UpdateShop(shop *domain.Shop) error {
	if err := r.db.Save(shop).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}

// DeleteShop soft deletes a shop
func (r *repository) DeleteShop(id uuid.UUID) error {
	if err := r.db.Delete(&domain.Shop{}, id).Error; err != nil {
		return errors.NewDatabaseError(err.Error())
	}
	return nil
}
